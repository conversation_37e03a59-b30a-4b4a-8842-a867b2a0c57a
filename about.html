<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes"
    />
    <title>About - Petal Hair Designs | Luxury Hair Salon Sydney</title>

    <!-- Mobile status bar styling -->
    <meta name="theme-color" content="#1a1a1a" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent"
    />

    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <!-- Load web fonts only on tablet/desktop to maximize mobile speed -->
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@300;500&family=Inter:wght@400;600&display=swap"
      media="(min-width: 769px)"
    />
    <noscript>
      <link
        rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600&display=swap"
        media="(min-width: 769px)"
      />
    </noscript>
    <!-- Critical Image Preloading for Instant Display -->
    <link
      rel="preload"
      as="image"
      href="Photos/Logo (2).png"
      fetchpriority="high"
    />
    <link rel="preload" as="image" href="Photos/1.jpg" fetchpriority="high" />
    <link rel="preload" as="image" href="Photos/2.webp" fetchpriority="high" />
    <link rel="preload" as="image" href="Photos/4.webp" fetchpriority="high" />
    <style>
      :root {
        --primary-black: #000000;
        --luxury-gold: #d4af37;
        --soft-white: #fefefe;
        --pearl-white: #f8f8f8;
        --charcoal: #1a1a1a;
        --silver: #c0c0c0;
        --shadow-light: rgba(0, 0, 0, 0.05);
        --shadow-medium: rgba(0, 0, 0, 0.15);
        --shadow-heavy: rgba(0, 0, 0, 0.25);
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      html {
        scroll-behavior: smooth;
      }

      body {
        font-family: "Inter", sans-serif;
        line-height: 1.7;
        color: var(--charcoal);
        background: var(--soft-white);
        overflow-x: hidden;
      }

      /* Ultra-Luxury Header */
      header {
        position: fixed;
        top: 0;
        width: 100%;
        z-index: 1000;
        background: #1a1a1a;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .header-container {
        max-width: 1400px;
        margin: 0 auto;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1.8rem 3rem;
      }

      .logo {
        display: flex;
        align-items: center;
        position: relative;
      }

      .logo img {
        height: 108px;
        width: auto;
        filter: brightness(0) invert(1) sepia(1) saturate(5) hue-rotate(35deg);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .logo:hover img {
        filter: brightness(0) invert(1) sepia(1) saturate(5) hue-rotate(35deg)
          brightness(1.1);
      }

      nav ul {
        display: flex;
        list-style: none;
        gap: 3rem;
        align-items: center;
      }

      nav a {
        color: var(--soft-white);
        text-decoration: none;
        font-weight: 400;
        font-size: 0.95rem !important;
        letter-spacing: 1px;
        text-transform: uppercase;
        position: relative;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }

      nav a::before {
        content: "";
        position: absolute;
        bottom: -8px;
        left: 50%;
        width: 0;
        height: 2px;
        background: var(--luxury-gold);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        transform: translateX(-50%);
      }

      nav a:hover::before,
      nav a.active::before {
        width: 100%;
      }

      nav a:hover {
        color: var(--luxury-gold);
      }

      .mobile-menu {
        display: none;
        flex-direction: column;
        cursor: pointer;
        padding: 5px;
        background: transparent;
        border: 0;
        align-items: center;
        justify-content: center;
      }

      .mobile-menu span {
        width: 28px;
        height: 2px;
        background: var(--soft-white);
        margin: 4px 0;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        border-radius: 2px;
      }

      /* Ultra-Premium Hero Section - Base */
      .hero {
        height: 100vh;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
      }

      /* Home Page Hero with Background Image */
      .hero-home {
        background: linear-gradient(
            135deg,
            rgba(0, 0, 0, 0.7) 0%,
            rgba(26, 26, 26, 0.5) 50%,
            rgba(0, 0, 0, 0.8) 100%
          ),
          url("Photos/Behind Hero.jpg");
        background-size: cover;
        background-position: center;
        background-attachment: fixed;
      }

      /* Other Pages Hero with Gradient Background */
      .hero-page {
        height: 70svh; /* Improved mobile viewport handling */
        min-height: 480px; /* Reduce excessive height on small devices */
        background: linear-gradient(
          135deg,
          var(--primary-black) 0%,
          var(--charcoal) 50%,
          var(--primary-black) 100%
        );
        /* Ensure perfect centering */
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .hero::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="0.5" fill="rgba(255,255,255,0.02)"/><circle cx="75" cy="75" r="0.3" fill="rgba(255,255,255,0.01)"/><circle cx="50" cy="10" r="0.4" fill="rgba(255,255,255,0.015)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
        animation: grain 20s linear infinite;
      }

      @keyframes grain {
        0%,
        100% {
          transform: translate(0, 0);
        }
        10% {
          transform: translate(-5%, -10%);
        }
        20% {
          transform: translate(-15%, 5%);
        }
        30% {
          transform: translate(7%, -25%);
        }
        40% {
          transform: translate(-5%, 25%);
        }
        50% {
          transform: translate(-15%, 10%);
        }
        60% {
          transform: translate(15%, 0%);
        }
        70% {
          transform: translate(0%, 15%);
        }
        80% {
          transform: translate(3%, 35%);
        }
        90% {
          transform: translate(-10%, 10%);
        }
      }

      .hero-content {
        text-align: center;
        z-index: 2;
        max-width: 900px;
        padding: 0 2rem;
        animation: heroFadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        opacity: 0;
        /* Ensure content is perfectly centered */
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      @keyframes heroFadeIn {
        0% {
          opacity: 0;
          transform: translateY(60px);
        }
        100% {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .hero-subtitle {
        font-family: "Inter", sans-serif;
        font-size: 0.95rem;
        font-weight: 400;
        color: var(--luxury-gold);
        letter-spacing: 4px;
        text-transform: uppercase;
        margin-bottom: 2rem;
        opacity: 0.9;
        animation: slideInFromTop 0.5s cubic-bezier(0.4, 0, 0.2, 1) 0.1s
          forwards;
        opacity: 0;
      }

      @keyframes slideInFromTop {
        0% {
          opacity: 0;
          transform: translateY(-30px);
        }
        100% {
          opacity: 0.9;
          transform: translateY(0);
        }
      }

      .hero h1 {
        font-family: "Playfair Display", serif;
        font-size: clamp(3rem, 6vw, 5rem);
        font-weight: 300;
        color: var(--soft-white);
        letter-spacing: 3px;
        line-height: 1.1;
        margin-bottom: 2.5rem;
        text-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
        animation: slideInFromBottom 0.5s cubic-bezier(0.4, 0, 0.2, 1) 0.2s
          forwards;
        opacity: 0;
      }

      @keyframes slideInFromBottom {
        0% {
          opacity: 0;
          transform: translateY(40px);
        }
        100% {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .hero-description {
        font-family: "Inter", sans-serif;
        font-size: 1.25rem;
        font-weight: 300;
        color: rgba(255, 255, 255, 0.85);
        line-height: 1.8;
        margin-bottom: 2.5rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
        animation: fadeInUp 0.5s cubic-bezier(0.4, 0, 0.2, 1) 0.3s forwards;
        opacity: 0;
      }

      @keyframes fadeInUp {
        0% {
          opacity: 0;
          transform: translateY(30px);
        }
        100% {
          opacity: 0.85;
          transform: translateY(0);
        }
      }

      /* Premium Content Sections */
      .luxury-section {
        padding: 8rem 0;
        position: relative;
      }

      .luxury-section:nth-child(even) {
        background: linear-gradient(
          180deg,
          var(--soft-white) 0%,
          var(--pearl-white) 100%
        );
      }

      .container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 0 3rem;
      }

      .section-header {
        text-align: center;
        margin-bottom: 5rem;
      }

      .section-subtitle {
        font-family: "Inter", sans-serif;
        font-size: 0.9rem;
        font-weight: 500;
        color: var(--luxury-gold);
        letter-spacing: 3px;
        text-transform: uppercase;
        margin-bottom: 1rem;
      }

      .section-title {
        font-family: "Playfair Display", serif;
        font-size: 2.5rem;
        font-weight: 300;
        color: var(--primary-black);
        letter-spacing: 2px;
        line-height: 1.2;
        margin-bottom: 2rem;
      }

      .section-description {
        font-size: 1.2rem;
        color: var(--charcoal);
        line-height: 1.8;
        max-width: 700px;
        margin: 0 auto;
        opacity: 0.9;
      }

      /* Luxury Grid Layouts */
      .luxury-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 3rem;
        margin-top: 4rem;
      }

      .luxury-card {
        background: var(--soft-white);
        padding: 3rem;
        border-radius: 0;
        box-shadow: 0 30px 80px var(--shadow-medium);
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        height: 100%;
        border-color: var(--luxury-gold);
      }

      .luxury-card::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background: linear-gradient(90deg, var(--luxury-gold), transparent);
        transform: scaleX(1);
        transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .card-icon {
        font-size: 2.5rem;
        margin-bottom: 2rem;
        color: var(--luxury-gold);
        text-align: center;
        display: block;
      }

      .card-title {
        font-family: "Playfair Display", serif;
        font-size: 1.8rem;
        font-weight: 400;
        color: var(--primary-black);
        margin-bottom: 1.5rem;
        letter-spacing: 1px;
        text-align: center;
      }

      .card-text {
        color: var(--charcoal);
        line-height: 1.8;
        opacity: 0.9;
        flex-grow: 1;
        text-align: center;
      }

      /* Timeline Styles */
      .story-timeline {
        position: relative;
        max-width: 1000px;
        margin: 4rem auto 0;
        padding: 2rem 0;
      }

      .story-timeline::before {
        content: "";
        position: absolute;
        left: 50%;
        top: 0;
        bottom: 0;
        width: 2px;
        background: linear-gradient(
          to bottom,
          var(--luxury-gold),
          rgba(212, 175, 55, 0.3)
        );
        transform: translateX(-50%);
      }

      .timeline-item {
        display: flex;
        align-items: center;
        margin-bottom: 4rem;
        position: relative;
      }

      .timeline-item:nth-child(odd) {
        flex-direction: row;
      }

      .timeline-item:nth-child(even) {
        flex-direction: row-reverse;
      }

      .timeline-year {
        flex-shrink: 0;
        width: 120px;
        height: 120px;
        background: linear-gradient(135deg, var(--luxury-gold), #b8941f);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-family: "Playfair Display", serif;
        font-size: 1.5rem;
        font-weight: 600;
        color: var(--primary-black);
        box-shadow: 0 10px 30px rgba(212, 175, 55, 0.3);
        position: relative;
        z-index: 2;
      }

      .timeline-content {
        flex: 1;
        background: var(--soft-white);
        padding: 2.5rem;
        border-radius: 15px;
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
        margin: 0 2rem;
        border: 1px solid rgba(212, 175, 55, 0.1);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .timeline-content:hover {
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.12);
        border-color: var(--luxury-gold);
      }

      .timeline-content h3 {
        font-family: "Playfair Display", serif;
        font-size: 1.6rem;
        font-weight: 500;
        color: var(--primary-black);
        margin-bottom: 1rem;
        letter-spacing: 0.5px;
      }

      .timeline-content p {
        color: var(--charcoal);
        line-height: 1.7;
        opacity: 0.9;
      }

      /* Team Styles */
      .team-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 3rem;
        margin-top: 4rem;
        align-items: stretch;
      }

      .team-member {
        background: var(--soft-white);
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid rgba(212, 175, 55, 0.1);
        display: flex;
        flex-direction: column;
        height: 100%;
        min-height: 500px;
      }

      .team-member:hover {
        box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
        border-color: var(--luxury-gold);
      }

      .member-image {
        height: 200px;
        background: linear-gradient(135deg, #f8f8f8, #f0f0f0);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
      }

      .member-photo {
        position: absolute;
        inset: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center top;
        z-index: 1;
      }

      /* Adjust positioning for Michelle Chen (2nd team member) */
      .team-member:nth-child(2) .member-photo {
        object-position: center 20%;
      }

      /* Adjust positioning for Emma Rodriguez (3rd team member) */
      .team-member:nth-child(3) .member-photo {
        object-position: center 15%;
      }

      .member-image::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="member-pattern" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="0.8" fill="rgba(212,175,55,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23member-pattern)"/></svg>');
        opacity: 0.6;
      }

      .member-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
        z-index: 2;
        position: relative;
      }

      .member-name {
        display: none;
      }

      .member-info {
        padding: 2.5rem;
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        text-align: center;
      }

      .member-info h3 {
        font-family: "Playfair Display", serif;
        font-size: 1.5rem;
        font-weight: 500;
        color: var(--primary-black);
        margin-bottom: 0.5rem;
        letter-spacing: 0.5px;
        text-align: center;
      }

      .member-title {
        color: var(--luxury-gold);
        font-weight: 600;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        margin-bottom: 1.5rem;
        text-align: center;
      }

      .member-bio {
        color: var(--charcoal);
        line-height: 1.7;
        margin-bottom: 2rem;
        opacity: 0.9;
        flex-grow: 1;
        min-height: 4.5rem;
        text-align: center;
      }

      .member-specialties {
        display: flex;
        flex-direction: column;
        gap: 0.8rem;
        margin-top: auto;
        min-height: 3rem;
        align-items: center;
      }

      .member-specialties span {
        background: linear-gradient(135deg, var(--luxury-gold), #b8941f);
        color: var(--primary-black);
        border-radius: 20px;
        font-weight: 600;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        transition: all 0.3s ease;
      }

      /* Badge sizes - smallest to largest (pyramid effect) */
      .member-specialties span:nth-child(1) {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
      }

      .member-specialties span:nth-child(2) {
        padding: 0.55rem 1.1rem;
        font-size: 0.85rem;
      }

      .member-specialties span:nth-child(3) {
        padding: 0.6rem 1.2rem;
        font-size: 0.85rem;
      }

      /* Values Grid */
      .values-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2.5rem;
        margin-top: 4rem;
      }

      .value-item {
        text-align: center;
        padding: 2.5rem 2rem;
        background: var(--soft-white);
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.06);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        border: 1px solid rgba(212, 175, 55, 0.1);
      }

      .value-item:hover {
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.12);
        border-color: var(--luxury-gold);
      }

      .value-icon {
        font-size: 3rem;
        margin-bottom: 1.5rem;
        display: block;
      }

      .value-item h3 {
        font-family: "Playfair Display", serif;
        font-size: 1.4rem;
        font-weight: 500;
        color: var(--primary-black);
        margin-bottom: 1rem;
        letter-spacing: 0.5px;
      }

      .value-item p {
        color: var(--charcoal);
        line-height: 1.7;
        opacity: 0.9;
      }

      /* Modern CTA Section */
      .modern-cta {
        padding: 8rem 0;
        background: linear-gradient(
          135deg,
          var(--pearl-white) 0%,
          var(--soft-white) 100%
        );
        position: relative;
        overflow: hidden;
      }

      .modern-cta::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="cta-pattern" width="50" height="50" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="0.8" fill="rgba(212,175,55,0.05)"/><circle cx="10" cy="40" r="0.4" fill="rgba(212,175,55,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23cta-pattern)"/></svg>');
        opacity: 0.6;
      }

      .cta-content {
        text-align: center;
        max-width: 800px;
        margin: 0 auto;
        position: relative;
        z-index: 2;
      }

      .cta-title {
        font-family: "Playfair Display", serif;
        font-size: clamp(2.5rem, 5vw, 3.5rem);
        font-weight: 300;
        color: var(--primary-black);
        margin-bottom: 1.5rem;
        letter-spacing: 2px;
        line-height: 1.2;
      }

      .cta-description {
        font-size: 1.2rem;
        color: var(--charcoal);
        line-height: 1.8;
        margin-bottom: 3rem;
        opacity: 0.9;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
      }

      .cta-actions {
        display: flex;
        justify-content: center;
        gap: 2rem;
        flex-wrap: wrap;
      }

      .cta-button {
        display: inline-flex;
        align-items: center;
        gap: 1rem;
        padding: 1.3rem 2.5rem;
        text-decoration: none;
        font-family: "Inter", sans-serif;
        font-weight: 600;
        font-size: 1rem;
        border-radius: 50px;
        letter-spacing: 0.5px;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        min-width: 200px;
        justify-content: center;
      }

      .cta-button.primary {
        background: linear-gradient(135deg, var(--luxury-gold), #b8941f);
        color: var(--primary-black);
        box-shadow: 0 10px 30px rgba(212, 175, 55, 0.3);
        border: 2px solid transparent;
      }

      .cta-button.primary::before {
        content: "";
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.2),
          transparent
        );
        transition: left 0.5s ease;
      }

      .cta-button.primary:hover::before {
        left: 100%;
      }

      .cta-button.primary:hover {
        box-shadow: 0 15px 40px rgba(212, 175, 55, 0.4);
        background: linear-gradient(135deg, #b8941f, var(--luxury-gold));
      }

      .cta-button.secondary {
        background: transparent;
        color: var(--primary-black);
        border: 2px solid var(--primary-black);
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
      }

      .cta-button.secondary:hover {
        background: var(--primary-black);
        color: var(--soft-white);
        transform: translateY(-3px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      }

      .button-text {
        font-weight: 600;
      }

      .button-icon {
        font-size: 1.2rem;
        transition: transform 0.3s ease;
      }

      .cta-button:hover .button-icon {
        color: var(--luxury-gold);
      }

      /* Desktop Navigation Font Size Consistency */
      @media (min-width: 769px) {
        nav a {
          font-size: 0.95rem !important;
        }
      }

      /* Mobile Responsive */
      @media (max-width: 768px) {
        .header-container {
          padding: 1rem 2rem;
        }

        .logo img {
          height: 70px;
          margin-top: 8px;
        }

        .hero {
          padding-top: 120px;
          height: 100vh;
          min-height: 100vh;
          background-attachment: scroll;
          background-size: cover;
          background-position: center;
          background-repeat: no-repeat;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .hero-content {
          padding: 1rem 2rem;
          max-width: 100%;
          gap: 1rem;
          text-align: center;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          transform: translateY(-60px);
        }

        .hero-subtitle {
          font-size: 0.8rem;
          letter-spacing: 2px;
          margin-bottom: 0.5rem;
        }

        .hero h1 {
          font-size: clamp(2rem, 7vw, 3.2rem);
          letter-spacing: 1px;
          line-height: 1.1;
          margin-bottom: 1rem;
          text-align: center;
        }

        .hero-description {
          font-size: 0.95rem;
          line-height: 1.5;
          margin-bottom: 1.5rem;
          text-align: center;
          max-width: 90%;
        }

        /* Speed up LCP on mobile by removing hero animations and making content visible immediately */
        .hero-content,
        .hero-subtitle,
        .hero h1,
        .hero-description,
        .cta-buttons {
          animation: none !important;
          opacity: 1 !important;
          transform: none !important;
        }

        .cta-buttons {
          flex-direction: column;
          gap: 1rem;
          width: 100%;
          max-width: 280px;
          align-items: center;
        }

        .cta-button {
          width: 100%;
          padding: 1rem 2rem;
          font-size: 0.95rem;
          text-align: center;
        }

        /* Reduce paint work on mobile: remove heavy shadows/filters in the hero */
        .hero h1 {
          text-shadow: none !important;
        }
        .cta-primary,
        .cta-secondary,
        .about-cta {
          box-shadow: none !important;
        }
        nav a {
          transition: none !important;
        }

        nav ul {
          display: none;
          position: fixed;
          top: 100px;
          left: 0;
          right: 0;
          width: 100vw;
          background: #1a1a1a;
          flex-direction: column;
          gap: 0;
          padding: 1rem 0;
          z-index: 999;
          border-top: 1px solid rgba(255, 255, 255, 0.1);
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
          margin: 0;
          max-height: calc(100vh - 100px);
          overflow-y: auto;
          -webkit-overflow-scrolling: touch;
        }

        nav ul.active {
          display: flex;
        }

        /* Reorder navigation for mobile - Book Now first */
        nav ul li:nth-child(1) {
          order: 2;
        } /* Home */
        nav ul li:nth-child(2) {
          order: 3;
        } /* About */
        nav ul li:nth-child(3) {
          order: 4;
        } /* Services */
        nav ul li:nth-child(4) {
          order: 5;
        } /* Gallery */
        nav ul li:nth-child(5) {
          order: 6;
        } /* Contact */
        nav ul li:nth-child(6) {
          order: 1;
        } /* Book Now - first */
        /* Ensure mobile navigation links display like Book Now */
        nav ul li a {
          display: block;
          padding: 1rem 2rem;
          text-align: center;
          border-bottom: 1px solid rgba(255, 255, 255, 0.05);
          width: 100%;
          box-sizing: border-box;
        }
        nav ul li:last-child a {
          border-bottom: none;
        }
        /* Align hover highlight with separator lines */
        nav a::before {
          display: none;
        }
        nav ul li a {
          transition: border-color 0.3s ease, border-bottom-width 0.2s ease;
        }
        nav ul li a:hover,
        nav ul li a:focus,
        nav ul li a.active {
          border-bottom-color: var(--luxury-gold);
          border-bottom-width: 2px;
        }
        nav ul li:last-child a {
          border-bottom: none !important;
        }

        .mobile-menu {
          display: flex;
        }

        .container {
          padding: 0 2rem;
        }

        .luxury-section {
          padding: 5rem 0;
        }

        .luxury-grid {
          grid-template-columns: 1fr;
          gap: 2rem;
        }

        .luxury-card {
          padding: 2rem;
        }

        /* Keep centered mobile layout but add center line */
        .story-timeline::before {
          left: 50%;
          transform: translateX(-50%);
        }

        .timeline-item {
          flex-direction: column !important;
          align-items: center !important;
          margin-left: 0;
        }

        .timeline-item:nth-child(odd),
        .timeline-item:nth-child(even) {
          flex-direction: column !important;
          align-items: center !important;
        }

        .timeline-year {
          width: 100px;
          height: 100px;
          font-size: 1.1rem;
          margin-bottom: 1rem;
        }

        .timeline-content {
          text-align: center !important;
          margin: 0;
          width: 90%;
          max-width: 400px;
        }

        .timeline-content h3,
        .timeline-item:nth-child(odd) .timeline-content h3,
        .timeline-item:nth-child(even) .timeline-content h3 {
          text-align: center !important;
          font-family: "Playfair Display", serif !important;
          font-size: 1.6rem !important;
          font-weight: 500 !important;
          color: var(--primary-black) !important;
          margin-bottom: 1rem !important;
          letter-spacing: 0.5px !important;
        }

        .timeline-content p,
        .timeline-item:nth-child(odd) .timeline-content p,
        .timeline-item:nth-child(even) .timeline-content p {
          text-align: center !important;
          color: var(--charcoal) !important;
          line-height: 1.7 !important;
          opacity: 0.9 !important;
        }

        .team-grid {
          grid-template-columns: 1fr;
          gap: 2rem;
        }

        .team-member {
          margin: 0 auto;
          max-width: 400px;
        }

        .values-grid {
          grid-template-columns: 1fr;
          gap: 2rem;
        }

        .value-item {
          padding: 2rem 1.5rem;
        }

        .modern-cta {
          padding: 5rem 0;
        }

        .cta-title {
          font-size: 2.2rem;
          margin-bottom: 1rem;
        }

        .cta-description {
          font-size: 1.1rem;
          margin-bottom: 2.5rem;
        }

        .cta-actions {
          flex-direction: column;
          align-items: center;
          gap: 1.5rem;
        }

        .cta-button {
          width: 100%;
          max-width: 280px;
          padding: 1.2rem 2rem;
          font-size: 0.95rem;
        }
      }

      /* Ultra-Premium Footer */
      footer {
        background: #1a1a1a;
        color: var(--soft-white);
        position: relative;
        overflow: hidden;
      }

      footer::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(
          90deg,
          transparent,
          var(--luxury-gold),
          transparent
        );
      }

      .footer-content {
        max-width: 1400px;
        margin: 0 auto;
        padding: 2.5rem 3rem 1.5rem;
        position: relative;
        z-index: 2;
      }

      .footer-grid {
        display: grid;
        grid-template-columns: 2fr 1fr 1fr 1fr;
        gap: 2rem;
        margin-bottom: 2rem;
      }

      .footer-brand {
        display: flex;
        flex-direction: column;
        gap: 1rem;
      }

      .footer-logo {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;
      }

      .footer-logo img {
        height: 80px;
        width: auto;
        filter: brightness(0) invert(1) sepia(1) saturate(5) hue-rotate(35deg);
        transition: all 0.3s ease;
      }

      .footer-logo:hover img {
        filter: brightness(1.5) contrast(1.6)
          drop-shadow(0 3px 10px rgba(212, 175, 55, 0.5));
      }

      .footer-description {
        font-size: 1rem;
        line-height: 1.8;
        opacity: 0.9;
        max-width: 300px;
      }

      .footer-section h3 {
        font-family: "Playfair Display", serif;
        font-size: 1.2rem;
        font-weight: 400;
        color: var(--luxury-gold);
        margin-bottom: 1rem;
        letter-spacing: 1px;
        text-transform: uppercase;
      }

      .footer-links {
        list-style: none;
        display: flex;
        flex-direction: column;
        gap: 0.6rem;
      }

      .footer-links a {
        color: var(--soft-white);
        text-decoration: none;
        font-size: 0.95rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        opacity: 0.8;
      }

      .footer-links a:hover {
        color: var(--luxury-gold);
        opacity: 1;
        transform: translateX(5px);
      }

      .footer-contact p {
        margin-bottom: 0.6rem;
        font-size: 0.95rem;
        opacity: 0.9;
      }

      .footer-contact a {
        color: var(--luxury-gold);
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
      }

      .footer-contact a:hover {
        color: var(--soft-white);
      }

      .footer-social {
        display: flex;
        gap: 0.8rem;
        margin-top: 1rem;
      }

      .social-link {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        color: var(--soft-white);
        text-decoration: none;
        font-size: 1.2rem;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .social-link:hover {
        background: var(--luxury-gold);
        color: var(--primary-black);
        transform: translateY(-3px);
      }

      .footer-bottom {
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        padding-top: 1.5rem;
        text-align: center;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
      }

      .footer-copyright {
        font-size: 0.9rem;
        opacity: 0.8;
      }

      .footer-credits {
        font-size: 0.9rem;
        opacity: 0.8;
      }

      .footer-credits a {
        color: var(--luxury-gold);
        text-decoration: none;
        font-weight: 500;
      }

      .footer-credits a:hover {
        color: var(--soft-white);
      }

      /* Opening Hours Styling */
      .opening-hours {
        margin-top: 1rem;
      }

      .hours-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.4rem 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      }

      .hours-row:last-child {
        border-bottom: none;
      }

      .day {
        font-size: 0.95rem;
        opacity: 0.9;
      }

      .time {
        font-size: 0.95rem;
        color: var(--luxury-gold);
        font-weight: 500;
        text-align: center;
      }

      @media (max-width: 768px) {
        .footer-content {
          padding: 2rem 2rem 1rem;
        }

        .footer-grid {
          grid-template-columns: 1fr;
          gap: 1.5rem;
          text-align: center;
        }

        .footer-brand {
          align-items: center;
        }

        .footer-description {
          max-width: 100%;
        }

        .footer-bottom {
          flex-direction: column;
          text-align: center;
        }

        /* Mobile opening hours styling */
        .hours-row {
          padding: 0.6rem 0;
          font-size: 0.9rem;
          justify-content: center;
        }

        .day,
        .time {
          font-size: 0.9rem;
          text-align: center;
          flex: 1;
        }

        /* Timeline mobile centering - duplicate to ensure it applies */
        .timeline-content {
          text-align: center !important;
        }

        .timeline-content h3 {
          text-align: center !important;
        }

        .timeline-content p {
          text-align: center !important;
        }
      }

      /* Scroll to Top Button - Mobile Only, Small Circle Design */
      .scroll-to-top {
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 40px;
        height: 40px;
        background: rgba(26, 26, 26, 0.9);
        color: var(--luxury-gold);
        border: 1px solid rgba(212, 175, 55, 0.3);
        border-radius: 50%;
        cursor: pointer;
        font-size: 0.9rem;
        font-weight: 600;
        display: none;
        align-items: center;
        justify-content: center;
        box-shadow: 0 3px 12px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transform: translateY(10px) scale(0.9);
      }

      .scroll-to-top.visible {
        opacity: 1;
        visibility: visible;
        transform: translateY(0) scale(1);
      }

      .scroll-to-top:hover {
        background: rgba(212, 175, 55, 0.9);
        color: var(--primary-black);
        border-color: var(--luxury-gold);
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 6px 20px rgba(212, 175, 55, 0.3);
      }

      .scroll-to-top:active {
        transform: translateY(0) scale(0.95);
      }

      /* Show only on mobile devices */
      @media (max-width: 768px) {
        .scroll-to-top {
          display: flex;
        }
      }
    </style>
  </head>
  <body>
    <header>
      <div class="header-container">
        <div class="logo">
          <a href="index.html">
            <img
              src="Photos/Logo (2).png"
              alt="Petal Hair Designs"
              width="160"
              height="80"
              fetchpriority="high"
              decoding="sync"
              loading="eager"
            />
          </a>
        </div>
        <nav>
          <ul id="nav-menu">
            <li><a href="index.html">Home</a></li>
            <li><a href="about.html" class="active">About</a></li>
            <li><a href="services.html">Services</a></li>
            <li><a href="gallery.html">Gallery</a></li>
            <li><a href="contact.html">Contact</a></li>
            <li><a href="book-now.html">Book Now</a></li>
          </ul>
          <button
            class="mobile-menu"
            type="button"
            aria-label="Toggle navigation"
            aria-controls="nav-menu"
            aria-expanded="false"
            onclick="toggleMenu()"
          >
            <span></span>
            <span></span>
            <span></span>
          </button>
        </nav>
      </div>
    </header>

    <section class="hero hero-page">
      <div class="hero-content">
        <div class="hero-subtitle">Our Story</div>
        <h1>About Petal</h1>
        <p class="hero-description">
          Discover the passion, artistry, and dedication behind Sydney's most
          prestigious hair salon, where luxury meets expertise.
        </p>
      </div>
    </section>

    <section class="luxury-section">
      <div class="container">
        <div class="section-header">
          <div class="section-subtitle">Excellence Defined</div>
          <h2 class="section-title">Our Philosophy</h2>
          <p class="section-description">
            At Petal Hair Designs, we believe that exceptional hair artistry is
            born from the perfect fusion of technical mastery, creative vision,
            and an unwavering commitment to luxury. Every service is a bespoke
            experience, tailored to enhance your natural beauty and elevate your
            confidence.
          </p>
        </div>

        <div class="luxury-grid">
          <div class="luxury-card">
            <div class="card-icon">✨</div>
            <h3 class="card-title">Artistry & Innovation</h3>
            <p class="card-text">
              Our master stylists combine time-honored techniques with
              cutting-edge innovations, ensuring every cut, color, and style
              represents the pinnacle of hair artistry.
            </p>
          </div>

          <div class="luxury-card">
            <div class="card-icon">💎</div>
            <h3 class="card-title">Luxury Experience</h3>
            <p class="card-text">
              From the moment you enter our salon, you're immersed in an
              atmosphere of refined elegance, where every detail is curated to
              provide an unparalleled luxury experience.
            </p>
          </div>

          <div class="luxury-card">
            <div class="card-icon">🎯</div>
            <h3 class="card-title">Personal Touch</h3>
            <p class="card-text">
              We understand that true beauty is individual. Our consultative
              approach ensures every service is perfectly tailored to your
              unique features, lifestyle, and personal style.
            </p>
          </div>
        </div>
      </div>
    </section>

    <section class="luxury-section">
      <div class="container">
        <div class="section-header">
          <div class="section-subtitle">Our Journey</div>
          <h2 class="section-title">The Petal Story</h2>
          <p class="section-description">
            Founded with a vision to redefine luxury hair care in Sydney, Petal
            Hair Designs has grown from a passionate dream into the city's most
            prestigious salon, setting new standards for excellence and
            sophistication.
          </p>
        </div>

        <div class="story-timeline">
          <div class="timeline-item">
            <div class="timeline-year">2009</div>
            <div class="timeline-content">
              <h3>The Beginning</h3>
              <p>
                Petal Hair Designs opened its doors with a simple yet ambitious
                mission: to bring world-class hair artistry to the vibrant city
                of Sydney.
              </p>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-year">2015</div>
            <div class="timeline-content">
              <h3>Recognition & Growth</h3>
              <p>
                Our commitment to excellence earned industry recognition and a
                loyal clientele, establishing us as the premier destination for
                luxury hair services.
              </p>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-year">2020</div>
            <div class="timeline-content">
              <h3>Innovation & Expansion</h3>
              <p>
                We expanded our services and embraced cutting-edge techniques
                while maintaining our core values of personalized luxury and
                artistic excellence.
              </p>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-year">2024</div>
            <div class="timeline-content">
              <h3>Continued Excellence</h3>
              <p>
                Today, we continue to set the standard for luxury hair care,
                combining 15+ years of expertise with the latest innovations in
                hair artistry.
              </p>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-year">2025</div>
            <div class="timeline-content">
              <h3>Future Vision</h3>
              <p>
                Looking ahead, we embrace new horizons in sustainable luxury and
                cutting-edge techniques, continuing our legacy of transformative
                beauty experiences for the next generation.
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="luxury-section">
      <div class="container">
        <div class="section-header">
          <div class="section-subtitle">Meet Our Team</div>
          <h2 class="section-title">Master Stylists</h2>
          <p class="section-description">
            Our team of award-winning stylists brings together decades of
            experience, continuous education, and an unwavering passion for
            creating beautiful, transformative hair experiences.
          </p>
        </div>

        <div class="team-grid">
          <div class="team-member">
            <div class="member-image">
              <img
                src="Photos/1.jpg"
                alt="Senior Stylist - Sarah Thompson"
                class="member-photo"
                loading="eager"
                fetchpriority="high"
                decoding="sync"
                width="400"
                height="500"
              />
              <div class="member-name">Senior Stylist</div>
            </div>
            <div class="member-info">
              <h3>Sarah Thompson</h3>
              <p class="member-title">Creative Director & Senior Stylist</p>
              <p class="member-bio">
                With over 15 years of experience and advanced training in London
                and Paris, Sarah brings international expertise and artistic
                vision to every client experience.
              </p>
              <div class="member-specialties">
                <span>Bridal Expert</span>
                <span>Color Specialist</span>
                <span>Cutting Techniques</span>
              </div>
            </div>
          </div>

          <div class="team-member">
            <div class="member-image">
              <img
                src="Photos/2.webp"
                alt="Master Colorist - Michelle Chen"
                class="member-photo"
                loading="eager"
                fetchpriority="high"
                decoding="sync"
                width="400"
                height="500"
              />
              <div class="member-name">Master Colorist</div>
            </div>
            <div class="member-info">
              <h3>Michelle Chen</h3>
              <p class="member-title">Master Colorist & Style Consultant</p>
              <p class="member-bio">
                Renowned for her innovative color techniques and precision
                cutting, Michelle has trained with industry leaders and brings
                cutting-edge artistry to every service.
              </p>
              <div class="member-specialties">
                <span>Modern Cuts</span>
                <span>Balayage Expert</span>
                <span>Color Correction</span>
              </div>
            </div>
          </div>

          <div class="team-member">
            <div class="member-image">
              <img
                src="Photos/4.webp"
                alt="Treatment Specialist - Emma Rodriguez"
                class="member-photo"
                loading="eager"
                fetchpriority="high"
                decoding="sync"
                width="400"
                height="500"
              />
              <div class="member-name">Treatment Specialist</div>
            </div>
            <div class="member-info">
              <h3>Emma Rodriguez</h3>
              <p class="member-title">Hair Health & Treatment Specialist</p>
              <p class="member-bio">
                Specializing in hair restoration and luxury treatments, Emma
                combines scientific knowledge with artistic skill to restore and
                enhance hair health and beauty.
              </p>
              <div class="member-specialties">
                <span>Scalp Health</span>
                <span>Hair Restoration</span>
                <span>Keratin Treatments</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <section class="luxury-section">
      <div class="container">
        <div class="section-header">
          <div class="section-subtitle">Our Commitment</div>
          <h2 class="section-title">Values & Standards</h2>
          <p class="section-description">
            Every aspect of our service is guided by unwavering principles that
            ensure exceptional experiences and outstanding results for every
            client.
          </p>
        </div>

        <div class="values-grid">
          <div class="value-item">
            <div class="value-icon">🏆</div>
            <h3>Excellence</h3>
            <p>
              We pursue perfection in every service, using only the finest
              products and most advanced techniques to deliver exceptional
              results.
            </p>
          </div>
          <div class="value-item">
            <div class="value-icon">💝</div>
            <h3>Personalization</h3>
            <p>
              Every client receives a bespoke experience tailored to their
              unique needs, preferences, and lifestyle requirements.
            </p>
          </div>
          <div class="value-item">
            <div class="value-icon">🌟</div>
            <h3>Innovation</h3>
            <p>
              We continuously embrace new techniques and technologies while
              honoring time-tested traditions of hair artistry.
            </p>
          </div>
          <div class="value-item">
            <div class="value-icon">🤝</div>
            <h3>Integrity</h3>
            <p>
              Honest consultation, transparent pricing, and genuine care for our
              clients' satisfaction guide every interaction.
            </p>
          </div>
          <div class="value-item">
            <div class="value-icon">🎨</div>
            <h3>Artistry</h3>
            <p>
              We view each service as an opportunity to create something
              beautiful, transformative, and uniquely suited to you.
            </p>
          </div>
          <div class="value-item">
            <div class="value-icon">💎</div>
            <h3>Luxury</h3>
            <p>
              From ambiance to service delivery, every detail is curated to
              provide an indulgent, memorable experience.
            </p>
          </div>
        </div>
      </div>
    </section>

    <section class="modern-cta">
      <div class="container">
        <div class="cta-content">
          <div class="cta-text">
            <h2 class="cta-title">Ready to Transform Your Look?</h2>
            <p class="cta-description">
              Experience the Petal difference with a personalized consultation.
              Let our master stylists create something extraordinary just for
              you.
            </p>
          </div>
          <div class="cta-actions">
            <a href="book-now.html" class="cta-button primary">
              <span class="button-text">Book Your Appointment</span>
              <span class="button-icon">→</span>
            </a>
            <a href="contact.html" class="cta-button secondary">
              <span class="button-text">Schedule Consultation</span>
              <span class="button-icon">📞</span>
            </a>
          </div>
        </div>
      </div>
    </section>

    <footer>
      <div class="footer-content">
        <div class="footer-grid">
          <!-- Brand Section -->
          <div class="footer-brand">
            <div class="footer-logo">
              <a href="index.html">
                <img
                  src="Photos/Logo (2).png"
                  alt="Petal Hair Designs"
                  width="160"
                  height="80"
                  loading="lazy"
                  decoding="async"
                />
              </a>
            </div>
            <p class="footer-description">
              Sydney's most prestigious hair salon, where luxury meets
              expertise. Experience the pinnacle of hair artistry and
              personalized beauty services.
            </p>
            <div class="footer-social">
              <a href="#" class="social-link" aria-label="Facebook">📘</a>
              <a href="#" class="social-link" aria-label="Instagram">📷</a>
              <a href="#" class="social-link" aria-label="Google">🌐</a>
            </div>
          </div>

          <!-- Quick Links -->
          <div class="footer-section">
            <h3>Quick Links</h3>
            <ul class="footer-links">
              <li><a href="index.html">Home</a></li>
              <li><a href="about.html">About Us</a></li>
              <li><a href="services.html">Services</a></li>
              <li><a href="gallery.html">Gallery</a></li>
              <li><a href="contact.html">Contact</a></li>
            </ul>
          </div>

          <!-- Services -->
          <div class="footer-section">
            <h3>Services</h3>
            <ul class="footer-links">
              <li><a href="services.html">Hair Cutting</a></li>
              <li><a href="services.html">Hair Coloring</a></li>
              <li><a href="services.html">Bridal Styling</a></li>
              <li><a href="services.html">Hair Treatments</a></li>
              <li><a href="contact.html">Consultation</a></li>
            </ul>
          </div>

          <!-- Contact Info -->
          <div class="footer-section">
            <h3>Contact</h3>
            <div class="footer-contact">
              <p>125 George Street<br />Sydney NSW 2000</p>
              <p><a href="tel:0270104829">(02) 7010 4829</a></p>
              <p>
                <a href="mailto:<EMAIL>"
                  ><EMAIL></a
                >
              </p>
              <div class="opening-hours">
                <div class="hours-row">
                  <span class="day"><strong>Sunday:</strong></span>
                  <span class="time">CLOSED</span>
                </div>
                <div class="hours-row">
                  <span class="day"><strong>Monday:</strong></span>
                  <span class="time">CLOSED</span>
                </div>
                <div class="hours-row">
                  <span class="day"><strong>Tuesday:</strong></span>
                  <span class="time">9am-5:30pm</span>
                </div>
                <div class="hours-row">
                  <span class="day"><strong>Wednesday:</strong></span>
                  <span class="time">9am-5:30pm</span>
                </div>
                <div class="hours-row">
                  <span class="day"><strong>Thursday:</strong></span>
                  <span class="time">9am-7pm</span>
                </div>
                <div class="hours-row">
                  <span class="day"><strong>Friday:</strong></span>
                  <span class="time">9am-5:30pm</span>
                </div>
                <div class="hours-row">
                  <span class="day"><strong>Saturday:</strong></span>
                  <span class="time">9am-3pm</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="footer-bottom">
          <div class="footer-copyright">
            &copy; 2025 Petal Hair Designs. All rights reserved.
          </div>
        </div>
      </div>
    </footer>

    <script>
      function toggleMenu() {
        const navMenu = document.getElementById("nav-menu");
        const menuButton = document.querySelector(".mobile-menu");
        const isActive = navMenu.classList.toggle("active");
        if (menuButton) {
          menuButton.setAttribute("aria-expanded", isActive ? "true" : "false");
        }
      }

      // Enhanced scroll effects - throttled for performance
      let ticking = false;
      function updateHeader() {
        const header = document.querySelector("header");
        if (window.scrollY > 100) {
          header.style.background = "rgba(26,26,26,0.98)";
        } else {
          header.style.background = "rgba(26,26,26,0.95)";
        }
        ticking = false;
      }

      window.addEventListener(
        "scroll",
        function () {
          if (!ticking) {
            requestAnimationFrame(updateHeader);
            ticking = true;
          }
        },
        { passive: true }
      );

      // Intersection Observer for animations
      const observerOptions = {
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px",
      };

      const observer = new IntersectionObserver(function (entries) {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.style.opacity = "1";
            entry.target.style.transform = "translateY(0)";
          }
        });
      }, observerOptions);

      // Observe cards for animation
      document.addEventListener("DOMContentLoaded", function () {
        const cards = document.querySelectorAll(".luxury-card");
        cards.forEach((card) => {
          card.style.opacity = "0";
          card.style.transform = "translateY(30px)";
          card.style.transition = "all 0.8s cubic-bezier(0.4, 0, 0.2, 1)";
          observer.observe(card);
        });
      });
    </script>

    <!-- Scroll to Top Button -->
    <button id="scrollToTop" class="scroll-to-top" aria-label="Scroll to top">
      <span>↑</span>
    </button>

    <script>
      // Scroll to top button functionality
      const scrollBtn = document.getElementById("scrollToTop");

      function toggleScrollButton() {
        if (window.scrollY > 100) {
          scrollBtn.classList.add("visible");
        } else {
          scrollBtn.classList.remove("visible");
        }
      }

      window.addEventListener("scroll", toggleScrollButton, { passive: true });
      scrollBtn.addEventListener("click", () => {
        window.scrollTo({ top: 0, behavior: "smooth" });
      });
      document.addEventListener("DOMContentLoaded", toggleScrollButton);

      // UNIVERSAL INSTANT LOADING - FORCE ALL IMAGES TO LOAD IMMEDIATELY
      (function forceInstantImageLoading() {
        function makeAllImagesInstant() {
          const allImages = document.querySelectorAll("img");

          allImages.forEach((img) => {
            img.loading = "eager";
            img.fetchPriority = "high";
            img.decoding = "sync";

            img.removeAttribute("loading");
            img.setAttribute("loading", "eager");
            img.setAttribute("fetchpriority", "high");
            img.setAttribute("decoding", "sync");

            img.classList.remove("lazy");

            if (img.dataset.src && !img.src) {
              img.src = img.dataset.src;
            }
          });
        }

        makeAllImagesInstant();
        if (document.readyState === "loading") {
          document.addEventListener("DOMContentLoaded", makeAllImagesInstant);
        }
        setTimeout(makeAllImagesInstant, 100);
        setTimeout(makeAllImagesInstant, 500);
      })();
    </script>
  </body>
</html>
