<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Header Test - Enlighten Hair Design</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1a1a1a;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .test-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: #fafafa;
        }
        .test-card h3 {
            color: #d4af37;
            margin-top: 0;
        }
        .page-link {
            display: inline-block;
            background: #1a1a1a;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            transition: background 0.3s;
        }
        .page-link:hover {
            background: #d4af37;
            color: #1a1a1a;
        }
        .instructions {
            background: #e8f4f8;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #d4af37;
        }
        .instructions h3 {
            color: #1a1a1a;
            margin-top: 0;
        }
        .test-checklist {
            list-style: none;
            padding: 0;
        }
        .test-checklist li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .test-checklist li:before {
            content: "☐ ";
            color: #d4af37;
            font-weight: bold;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Header Functionality Test Suite</h1>
        
        <div class="instructions">
            <h3>📋 Testing Instructions</h3>
            <p>Use the links below to test each page. For each page, verify both mobile and desktop functionality:</p>
            <ul class="test-checklist">
                <li>Header maintains consistent color when scrolling up and down</li>
                <li>Mobile dropdown menu appears correctly when clicking hamburger menu</li>
                <li>Mobile dropdown has consistent styling (background, size, colors)</li>
                <li>Mobile dropdown shows "Book Now" first in the list</li>
                <li>Desktop navigation works properly</li>
                <li>Header background is consistent across all pages</li>
            </ul>
        </div>

        <div class="test-grid">
            <div class="test-card">
                <h3>🏠 Home Page</h3>
                <p>Test the main landing page header functionality.</p>
                <a href="index.html" class="page-link" target="_blank">Open Home Page</a>
            </div>

            <div class="test-card">
                <h3>👤 About Page</h3>
                <p>Test the about page header functionality.</p>
                <a href="about.html" class="page-link" target="_blank">Open About Page</a>
            </div>

            <div class="test-card">
                <h3>✂️ Services Page</h3>
                <p>Test the services page header functionality.</p>
                <a href="services.html" class="page-link" target="_blank">Open Services Page</a>
            </div>

            <div class="test-card">
                <h3>🖼️ Gallery Page</h3>
                <p>Test the gallery page header functionality.</p>
                <a href="gallery.html" class="page-link" target="_blank">Open Gallery Page</a>
            </div>

            <div class="test-card">
                <h3>📞 Contact Page</h3>
                <p>Test the contact page header functionality.</p>
                <a href="contact.html" class="page-link" target="_blank">Open Contact Page</a>
            </div>

            <div class="test-card">
                <h3>📅 Book Now Page</h3>
                <p>Test the booking page header functionality (previously had issues).</p>
                <a href="book-now.html" class="page-link" target="_blank">Open Book Now Page</a>
            </div>
        </div>

        <div class="instructions">
            <h3>🔧 What Was Fixed</h3>
            <ul>
                <li><strong>Header Color Bug:</strong> Standardized scroll event handlers across all pages to use consistent colors</li>
                <li><strong>Mobile Dropdown:</strong> Ensured all pages have identical mobile dropdown styling and behavior</li>
                <li><strong>Scroll Colors:</strong> Fixed gallery.html which had incorrect static colors</li>
                <li><strong>Consistency:</strong> All pages now use rgba(26,26,26,0.98) when scrolled and rgba(26,26,26,0.95) at top</li>
            </ul>
        </div>

        <div class="instructions">
            <h3>📱 Mobile Testing</h3>
            <p>To test mobile functionality:</p>
            <ol>
                <li>Open browser developer tools (F12)</li>
                <li>Click the device toggle button (mobile view)</li>
                <li>Select a mobile device or set custom dimensions</li>
                <li>Test the hamburger menu on each page</li>
                <li>Verify dropdown styling is consistent</li>
            </ol>
        </div>
    </div>
</body>
</html>
